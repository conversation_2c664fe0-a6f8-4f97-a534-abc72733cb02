# 设计文档

## 概述

本设计文档详细描述了二次元周边商品团购Android应用的技术架构、组件设计和用户界面设计。应用采用现代化的技术栈，包括React Native前端、Java Spring Boot后端、MySQL数据库，以及RESTful API通信协议。

设计目标是创建一个用户友好、性能优异、可扩展的团购平台，专门服务于二次元爱好者群体。

## 架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native  │    │   Spring Boot   │    │     MySQL       │
│   Mobile App    │◄──►│   Backend API   │◄──►│   Database      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ UI/UX   │             │ Business│             │ Data    │
    │ Layer   │             │ Logic   │             │ Layer   │
    └─────────┘             └─────────┘             └─────────┘
```

### 前端架构 (React Native)

- **导航层**: React Navigation 6.x 用于页面路由管理
- **状态管理**: Redux Toolkit + RTK Query 用于全局状态和API调用
- **UI组件**: 基于Tailwind CSS的自定义组件库
- **本地存储**: AsyncStorage 用于用户偏好和缓存
- **图片处理**: React Native Fast Image 用于图片优化
- **推送通知**: React Native Push Notification

### 后端架构 (Java Spring Boot)

- **Web层**: Spring MVC + RESTful API
- **业务层**: Spring Service + 业务逻辑处理
- **数据层**: Spring Data JPA + MySQL
- **安全层**: Spring Security + JWT认证
- **缓存层**: Redis 用于会话和热点数据缓存
- **消息队列**: RabbitMQ 用于异步任务处理

### 数据库设计 (MySQL)

主要数据表结构：
- users (用户表)
- products (商品表)
- group_buy_activities (团购活动表)
- orders (订单表)
- order_items (订单项表)
- user_addresses (用户地址表)
- reviews (评价表)
- notifications (通知表)

## 组件和接口

### 前端组件架构

#### 1. 核心组件

**AuthStack (认证栈)**
- LoginScreen: 登录界面
- RegisterScreen: 注册界面
- ForgotPasswordScreen: 忘记密码界面

**MainStack (主应用栈)**
- TabNavigator: 底部导航栏
  - HomeStack: 首页栈
  - CategoryStack: 分类栈
  - CartStack: 购物车栈
  - ProfileStack: 个人中心栈

**共享组件**
- ProductCard: 商品卡片组件
- GroupBuyTimer: 团购倒计时组件
- CustomButton: 自定义按钮组件
- LoadingSpinner: 加载动画组件
- ImageCarousel: 图片轮播组件

#### 2. 页面组件设计

**首页 (HomeScreen)**
```jsx
// 使用Tailwind CSS类名的现代化设计
<View className="flex-1 bg-gray-50">
  <ScrollView className="flex-1">
    {/* 轮播图 */}
    <ImageCarousel 
      images={bannerImages}
      className="h-48 mb-4"
    />
    
    {/* 分类导航 */}
    <View className="flex-row justify-around bg-white py-4 mb-4">
      {categories.map(category => (
        <CategoryIcon key={category.id} {...category} />
      ))}
    </View>
    
    {/* 热门团购 */}
    <View className="px-4">
      <Text className="text-xl font-bold text-gray-800 mb-4">
        🔥 热门团购
      </Text>
      <FlatList
        data={hotGroupBuys}
        renderItem={({item}) => <ProductCard product={item} />}
        numColumns={2}
        columnWrapperStyle={{justifyContent: 'space-between'}}
      />
    </View>
  </ScrollView>
</View>
```

**商品详情页 (ProductDetailScreen)**
- 商品图片轮播
- 商品基本信息展示
- 团购进度条和倒计时
- 规格选择器
- 用户评价列表
- 底部操作栏（加入购物车/立即购买）

**团购活动页 (GroupBuyScreen)**
- 活动详情展示
- 参与用户头像列表
- 进度指示器
- 分享功能
- 参与按钮

### 后端API接口设计

#### 1. 用户认证接口

```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@RequestBody RegisterRequest request);
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request);
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody RefreshTokenRequest request);
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(@RequestHeader("Authorization") String token);
}
```

#### 2. 商品管理接口

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @GetMapping
    public ResponseEntity<PagedResponse<ProductDTO>> getProducts(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) String category,
        @RequestParam(required = false) String keyword
    );
    
    @GetMapping("/{id}")
    public ResponseEntity<ProductDetailDTO> getProductDetail(@PathVariable Long id);
    
    @GetMapping("/{id}/reviews")
    public ResponseEntity<PagedResponse<ReviewDTO>> getProductReviews(@PathVariable Long id);
}
```

#### 3. 团购活动接口

```java
@RestController
@RequestMapping("/api/group-buy")
public class GroupBuyController {
    
    @GetMapping
    public ResponseEntity<PagedResponse<GroupBuyActivityDTO>> getGroupBuyActivities();
    
    @PostMapping("/{activityId}/join")
    public ResponseEntity<OrderDTO> joinGroupBuy(
        @PathVariable Long activityId,
        @RequestBody JoinGroupBuyRequest request
    );
    
    @GetMapping("/{activityId}/participants")
    public ResponseEntity<List<ParticipantDTO>> getParticipants(@PathVariable Long activityId);
}
```

## 数据模型

### 核心数据模型

#### 用户模型 (User)
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String phone;
    
    @Column(nullable = false)
    private String password;
    
    private String nickname;
    private String avatar;
    private Date createdAt;
    private Date updatedAt;
    
    @OneToMany(mappedBy = "user")
    private List<Order> orders;
    
    @OneToMany(mappedBy = "user")
    private List<UserAddress> addresses;
}
```

#### 商品模型 (Product)
```java
@Entity
@Table(name = "products")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(nullable = false)
    private BigDecimal originalPrice;
    
    private String category;
    private String brand;
    private String images; // JSON格式存储图片URL数组
    private Integer stock;
    private Date createdAt;
    
    @OneToMany(mappedBy = "product")
    private List<GroupBuyActivity> groupBuyActivities;
}
```

#### 团购活动模型 (GroupBuyActivity)
```java
@Entity
@Table(name = "group_buy_activities")
public class GroupBuyActivity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "product_id")
    private Product product;
    
    @Column(nullable = false)
    private BigDecimal groupPrice;
    
    @Column(nullable = false)
    private Integer minParticipants;
    
    @Column(nullable = false)
    private Integer maxParticipants;
    
    private Integer currentParticipants = 0;
    
    @Column(nullable = false)
    private Date startTime;
    
    @Column(nullable = false)
    private Date endTime;
    
    @Enumerated(EnumType.STRING)
    private GroupBuyStatus status;
    
    @OneToMany(mappedBy = "groupBuyActivity")
    private List<Order> orders;
}
```

## 错误处理

### 前端错误处理策略

1. **网络错误处理**
   - 自动重试机制
   - 离线状态检测
   - 友好的错误提示

2. **表单验证**
   - 实时验证反馈
   - 统一的验证规则
   - 错误信息本地化

3. **异常捕获**
   - 全局错误边界
   - 错误日志收集
   - 崩溃恢复机制

### 后端错误处理

```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(ValidationException e) {
        return ResponseEntity.badRequest()
            .body(new ErrorResponse("VALIDATION_ERROR", e.getMessage()));
    }
    
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleResourceNotFound(ResourceNotFoundException e) {
        return ResponseEntity.notFound()
            .build();
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(new ErrorResponse("INTERNAL_ERROR", "服务器内部错误"));
    }
}
```

## 测试策略

### 前端测试

1. **单元测试**
   - Jest + React Native Testing Library
   - 组件逻辑测试
   - 工具函数测试

2. **集成测试**
   - API调用测试
   - 导航流程测试
   - 状态管理测试

3. **端到端测试**
   - Detox 自动化测试
   - 关键用户流程测试

### 后端测试

1. **单元测试**
   - JUnit 5 + Mockito
   - Service层业务逻辑测试
   - Repository层数据访问测试

2. **集成测试**
   - Spring Boot Test
   - API接口测试
   - 数据库集成测试

3. **性能测试**
   - JMeter 压力测试
   - 数据库查询优化测试

## UI/UX设计规范

### 设计系统

#### 颜色方案
```css
/* 主色调 - 二次元风格 */
primary: #FF6B9D (粉色主色)
secondary: #4ECDC4 (青色辅助色)
accent: #FFE66D (黄色强调色)

/* 中性色 */
gray-50: #F9FAFB
gray-100: #F3F4F6
gray-200: #E5E7EB
gray-800: #1F2937
gray-900: #111827

/* 状态色 */
success: #10B981
warning: #F59E0B
error: #EF4444
info: #3B82F6
```

#### 字体系统
```css
/* 字体大小 */
text-xs: 12px
text-sm: 14px
text-base: 16px
text-lg: 18px
text-xl: 20px
text-2xl: 24px
text-3xl: 30px

/* 字重 */
font-normal: 400
font-medium: 500
font-semibold: 600
font-bold: 700
```

#### 间距系统
```css
/* Tailwind CSS间距 */
space-1: 4px
space-2: 8px
space-3: 12px
space-4: 16px
space-6: 24px
space-8: 32px
space-12: 48px
```

### 关键页面设计

#### 1. 启动页和引导页
- 二次元风格的启动动画
- 简洁的功能介绍引导
- 现代化的渐变背景

#### 2. 登录注册页面
- 简洁的表单设计
- 社交登录选项
- 友好的错误提示

#### 3. 首页设计
- 卡片式商品展示
- 清晰的分类导航
- 吸引人的团购倒计时

#### 4. 商品详情页
- 沉浸式图片展示
- 清晰的价格对比
- 直观的团购进度

#### 5. 购物车和结算页
- 简洁的商品列表
- 清晰的价格计算
- 流畅的支付流程

#### 6. 团购活动管理页面

**团购活动列表页**
- 活动状态标签（进行中/即将开始/已结束）
- 实时参与人数和进度条
- 倒计时显示
- 快速筛选和排序功能

**团购活动详情页**
```jsx
<ScrollView className="flex-1 bg-white">
  {/* 商品信息区域 */}
  <View className="p-4 border-b border-gray-200">
    <Image 
      source={{uri: product.image}} 
      className="w-full h-64 rounded-lg mb-4"
    />
    <Text className="text-xl font-bold text-gray-800 mb-2">
      {product.name}
    </Text>
    <View className="flex-row items-center justify-between">
      <View>
        <Text className="text-sm text-gray-500 line-through">
          原价: ¥{product.originalPrice}
        </Text>
        <Text className="text-2xl font-bold text-pink-500">
          团购价: ¥{groupBuy.groupPrice}
        </Text>
      </View>
      <View className="bg-pink-100 px-3 py-1 rounded-full">
        <Text className="text-pink-600 font-medium">
          省¥{product.originalPrice - groupBuy.groupPrice}
        </Text>
      </View>
    </View>
  </View>

  {/* 团购进度区域 */}
  <View className="p-4 border-b border-gray-200">
    <View className="flex-row items-center justify-between mb-2">
      <Text className="text-lg font-semibold text-gray-800">
        团购进度
      </Text>
      <Text className="text-sm text-gray-500">
        {groupBuy.currentParticipants}/{groupBuy.minParticipants}人
      </Text>
    </View>
    
    {/* 进度条 */}
    <View className="w-full bg-gray-200 rounded-full h-3 mb-3">
      <View 
        className="bg-gradient-to-r from-pink-400 to-pink-600 h-3 rounded-full"
        style={{width: `${(groupBuy.currentParticipants / groupBuy.minParticipants) * 100}%`}}
      />
    </View>
    
    {/* 倒计时 */}
    <GroupBuyTimer 
      endTime={groupBuy.endTime}
      className="bg-gray-50 p-3 rounded-lg"
    />
  </View>

  {/* 参与用户头像列表 */}
  <View className="p-4 border-b border-gray-200">
    <Text className="text-lg font-semibold text-gray-800 mb-3">
      已参与用户 ({groupBuy.currentParticipants})
    </Text>
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      {participants.map(user => (
        <View key={user.id} className="mr-3 items-center">
          <Image 
            source={{uri: user.avatar}} 
            className="w-12 h-12 rounded-full mb-1"
          />
          <Text className="text-xs text-gray-600 text-center">
            {user.nickname}
          </Text>
        </View>
      ))}
    </ScrollView>
  </View>

  {/* 商品详情 */}
  <View className="p-4">
    <Text className="text-lg font-semibold text-gray-800 mb-3">
      商品详情
    </Text>
    <Text className="text-gray-600 leading-6">
      {product.description}
    </Text>
  </View>
</ScrollView>

{/* 底部操作栏 */}
<View className="flex-row bg-white border-t border-gray-200 p-4">
  <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-lg mr-3">
    <Text className="text-center text-gray-700 font-medium">
      分享给好友
    </Text>
  </TouchableOpacity>
  
  <TouchableOpacity 
    className={`flex-2 py-3 rounded-lg ${
      groupBuy.status === 'ACTIVE' 
        ? 'bg-pink-500' 
        : 'bg-gray-400'
    }`}
    disabled={groupBuy.status !== 'ACTIVE'}
  >
    <Text className="text-center text-white font-bold">
      {groupBuy.status === 'ACTIVE' ? '立即参团' : '团购已结束'}
    </Text>
  </TouchableOpacity>
</View>
```

**团购创建/管理页面（管理员功能）**
- 商品选择器
- 团购参数设置（价格、人数、时间）
- 活动状态管理
- 参与者管理
- 数据统计展示

**我的团购页面**
- 参与的团购活动列表
- 团购状态跟踪
- 成功/失败通知
- 退款申请入口

### 交互设计原则

1. **一致性**: 统一的交互模式和视觉风格
2. **反馈性**: 及时的操作反馈和状态提示
3. **易用性**: 符合用户习惯的操作流程
4. **可访问性**: 支持无障碍访问功能
5. **性能**: 流畅的动画和快速的响应