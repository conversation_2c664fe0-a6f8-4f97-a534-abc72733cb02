---
type: "always_apply"
---

# Technology Stack

## Frontend
- **Framework**: React Native with Expo CLI
- **Navigation**: React Navigation 6.x
- **State Management**: Redux Toolkit + RTK Query
- **Styling**: Tailwind CSS with custom component library
- **Storage**: AsyncStorage for local data
- **Images**: React Native Fast Image for optimization
- **Notifications**: React Native Push Notification

## Backend
- **Framework**: Java Spring Boot
- **Architecture**: Spring MVC + RESTful API
- **Security**: Spring Security + JWT authentication
- **Database**: MySQL with Spring Data JPA
- **Caching**: Redis for sessions and hot data
- **Message Queue**: RabbitMQ for async tasks

## Database
- **Primary**: MySQL
- **Key Tables**: users, products, group_buy_activities, orders, order_items, user_addresses, reviews, notifications

## Common Commands

### Development Setup
```bash
# Frontend (React Native)
npx expo install
npm start

# Backend (Spring Boot)
./mvnw spring-boot:run

# Database
mysql -u root -p
```

### Testing
```bash
# Frontend tests
npm test
npm run test:e2e

# Backend tests
./mvnw test
./mvnw integration-test
```

### Build & Deploy
```bash
# Frontend build
expo build:android
expo build:ios

# Backend build
./mvnw clean package
```

## Development Environment
- Node.js for React Native development
- Java 11+ for Spring Boot
- MySQL 8.0+
- Redis for caching